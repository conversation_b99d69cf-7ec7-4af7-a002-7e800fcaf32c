import React, { useEffect, useRef } from 'react';

const HelloWC = ({ name = 'world' }) => {
	const scriptLoaded = useRef(false);

	useEffect(() => {
		// 如果脚本已经加载过，则不再重复添加
		if (scriptLoaded.current) return;

		// 检查脚本是否已存在（例如其他实例已加载）
		const existingScript = document.querySelector('script[src="/static/js/main.46625282.js"]');
		if (existingScript) {
			scriptLoaded.current = true;
			return;
		}

		// 动态创建 script 标签
		const script = document.createElement('script');
		script.src = 'http://*************:8081/static/js/main.46625282.js';
		script.defer = true;
		script.onload = () => {
			console.log('Web Component script loaded successfully.');
			scriptLoaded.current = true;
		};
		script.onerror = () => {
			console.error('Failed to load Web Component script.');
		};

		// 将 script 标签添加到 document.head
		document.head.appendChild(script);

		// 清理函数：理论上单个脚本可共享，但若组件非常特殊地需要卸载脚本，可在此处理
		// 但通常不建议移除，以免影响其他实例。
		return () => {
			// 谨慎操作：通常不移除脚本，因为它可能被多个组件实例依赖。
			// document.head.removeChild(script);
		};
	}, []); // 空依赖数组，确保effect只执行一次

	// 渲染 Web Component 自定义元素，并将 props 映射为属性
	return React.createElement('hello-wc', { name });
};


const HelloApp: React.FC<{
	name: string
}> = ({ name }) => {
	return <h1>
		Hello1, {name}!
		<HelloWC/>
	</h1>;
};

export default HelloApp;
